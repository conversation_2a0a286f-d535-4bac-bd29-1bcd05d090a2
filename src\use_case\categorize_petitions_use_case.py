from models import DetailedPetitionCategory, PetitionOutput
from repository.file_repository import (
    get_ground_truth,
    get_petition_data,
    write_petition_data,
)
from repository.openai_repository import TokenUsage, get_ai_category_response


def categorize_petitions_use_case(input_path: str, output_path: str) -> None:
    """
    Orchestrates the petition categorization process.

    Args:
        input_path: The path to the input JSON file.
        output_path: The path to the output JSON file.
    """
    petition_data = get_petition_data(input_path)
    output_data = []

    # Initialize total token usage tracking
    total_usage = TokenUsage()

    # TODO 先跑200筆試試看
    for i, petition in enumerate(petition_data[:100]):
        print(f"Processing petition {i}/{min(5, len(petition_data))}...")

        ground_truth = get_ground_truth(petition.sub_category)
        ai_response, usage = get_ai_category_response(petition.petition_content)

        # Accumulate token usage
        total_usage.prompt_tokens += usage.prompt_tokens
        total_usage.completion_tokens += usage.completion_tokens
        total_usage.total_tokens += usage.total_tokens
        total_usage.total_cost_usd += usage.total_cost_usd

        if ai_response:
            new_category = ai_response.new_category
            try:
                # validated_category = PetitionCategory(new_category)
                validated_category = DetailedPetitionCategory(new_category)
                lst_category = validated_category.value.split("_")
                main_category = lst_category[0]
                sub_category = lst_category[1]
                is_accurate = sub_category in ground_truth
            except ValueError:
                print(f"Invalid category: {new_category}")
                main_category = "analysis_failed"
                sub_category = "analysis_failed"
                is_accurate = False
        else:
            main_category = "analysis_failed"
            sub_category = "analysis_failed"
            is_accurate = False

        output_data.append(
            PetitionOutput(
                original_data=petition.model_dump(),
                main_category=main_category,
                sub_category=sub_category,
                ground_truth_category=ground_truth,
                is_accurate=is_accurate,
            )
        )

    write_petition_data(output_path, output_data)

    # Print cost summary
    print("\n" + "=" * 50)
    print("OpenAI API Usage Summary")
    print("=" * 50)
    print(f"Total petitions processed: {len(output_data)}")
    print(f"Prompt tokens: {total_usage.prompt_tokens:,}")
    print(f"Completion tokens: {total_usage.completion_tokens:,}")
    print(f"Total tokens: {total_usage.total_tokens:,}")
    print(f"Total cost: ${total_usage.total_cost_usd:.4f} USD")
    print("=" * 50)
