import logging
from dataclasses import dataclass

import instructor
import openai
from tenacity import (
    RetryError,
    before_sleep_log,
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_fixed,
)

from models import AICategoryResponse


@dataclass
class TokenUsage:
    """Store token usage and cost information"""

    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0
    total_cost_usd: float = 0.0


# GPT-4o-mini pricing per 1K tokens
INPUT_COST_PER_1K = 0.0004  # USD
OUTPUT_COST_PER_1K = 0.0016  # USD


def calculate_cost(prompt_tokens: int, completion_tokens: int) -> float:
    """Calculate total cost in USD based on token usage"""
    input_cost = (prompt_tokens / 1000) * INPUT_COST_PER_1K
    output_cost = (completion_tokens / 1000) * OUTPUT_COST_PER_1K
    return input_cost + output_cost


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# This file will contain all interactions with the OpenAI API.


PROMPT_TEMPLATE_OLD = """
你是一個頂尖的「1999 市政案件自動分類 AI 助手」。你的任務是深度分析市民的陳情內容，嚴格遵循指定的思考框架與輸出格式，將其精準地分類到最適當的案件類別。

## 核心任務 (Core Task)
接收 `<case_content>` 中的市民陳情內容，從 `<category_list>` 中選擇一個且僅一個最匹配的「類別名稱」，並以指定的 JSON 格式輸出。

## 思考框架 (SOP)

你必須嚴格遵循以下思考步驟，但絕對不要輸出思考過程：

1. **摘要與關鍵詞提取**：通讀案件內容，總結核心訴求，並提取出關鍵人、事、時、地、物。
2. **意圖分析**：判斷陳情者的主要意圖是「檢舉告發」、「請求協助」、「諮詢問題」、還是「提出建議」。
3. **案件類別選擇**：根據摘要、關鍵詞與意圖，從 `<category_list>`的「案件類別」清單中，選擇最精確、最貼切的一項。
4. **最終確認**:
	a. 檢查選擇的類別是否最能反映案件的核心問題。
	b. 如果案件內容模糊，找不到精確的類別，請優先選擇相關類別的 ..._其他 選項。
	c. 若沒有任何一個類別相符，才使用最末端的「其他類別」中的「其他建議、諮詢或陳情」。

## 規則與限制 (Rules & Constraints)
- **嚴格匹配**：案件類別的名稱必須與 `<category_list>` 中提供的「類別名稱」一字不差。嚴禁自創、修改或縮寫任何類別名稱。
- **唯一輸出**：你的回應**只能是**一個不含任何 Markdown 標記、註解或額外文字的 JSON 物件。
- **格式固定**：輸出的 JSON 必須嚴格遵守以下結構：`{ "category": "<選擇的類別名稱>" }`。

## 結構化案件類別清單 (Category List)
<category_list>
- 造成民眾生命危險
- 影響民眾身體健康
- 暴力威脅事件
- 涉及婦幼及學童議題
- 民生需求中斷
- 其他應緊急處理案件

- 檢舉交通違規
- 交通罰單申訴
- 車輛拖吊爭議
- 交通疏導或壅塞通報
- 闖紅燈（超速）照相桿增設或維護
- 監視器問題
- 治安維護
- 妨害風化（俗）
- 警政風紀
- 警政及交通裁罰業務_其他

- 占用道路、騎樓及人行道
- 有牌廢棄車查報
- 無牌廢棄車查報
- 廣告車輛長期占用停車格
- 違規停車
- 路霸排除_其他

- 路面不平整或掏空破洞
- 路面油漬清除
- 道路側溝清淤或惡臭處理
- 水溝溝蓋維修
- 道路施工時間、交通管制及安全管理問題
- 道路淹（積）水通報
- 電纜下地或纜線垂落
- 孔蓋異音
- 道路、水溝維護_其他

- 路燈故障
- 路燈新增或遷移申請
- 公園設施損壞
- 路樹傾倒
- 公園、綠地及路樹養護
- 新闢公園建議案
- 路燈、路樹及公園管理維護_其他

- 交通號誌(紅綠燈)故障或損壞傾斜
- 交通標誌牌面、反光鏡損壞傾斜
- 交通號誌增設、移除或紅綠燈秒數調整
- 交通標誌、標線、反光鏡設置或移除
- 路邊停車格問題
- 停車費問題
- 公車問題
- 站牌、候車亭設施管理
- 免費市民公車（樂活巴）問題
- 計程車問題及招呼站設施管理
- 公車動態系統問題
- 公共自行車（UBike）租賃問題
- 捷運營運及管理
- 捷運建設工程相關問題
- 交通號誌、標誌、標線及大眾運輸_其他

- 髒亂點查報
- 犬貓屍體清除
- 違規張貼廣告物
- 廢棄物清運預約
- 廢棄物管理（含清除、處理、再利用、輸出）
- 垃圾車清運動線及管理
- 住宅內人與動物噪音
- 住家、改裝車噪音
- 營業場所、工廠及施工噪音
- 空氣污染
- 工廠排放廢水、河川污染
- 綜合性環境污染
- 噪音、污染及環境維護_其他

- 社會住宅管理
- 一般違建查報
- 興建中違建查報
- 已查報違建拆除問題
- 違規招牌或樹立廣告物查報
- 公寓大廈管理問題
- 建築法規問題
- 建築物公共安全問題
- 領有建造執照施工損鄰
- 建築管理_其他

- 捕蜂、抓蛇
- 動物受困、受傷通報
- 動物收容及認養問題
- 遛狗未繫繩
- 不當飼養
- 非法業者及業者違規問題
- 動物保護及寵物管理_其他

- 教師介聘甄選
- 高級中等學校問題
- 國中學校問題
- 國小學校問題
- 補教問題
- 幼兒園問題
- 社區大學、樂齡學習等終身教育問題

- 特殊教育問題
- 學校體育問題
- 體育活動及場務管理
- 教育及體育_其他

- 勞工法令諮詢
- 勞資糾紛協調
- 就業服務及職業訓練
- 移工業務
- 檢舉公司（雇主）違反勞動法規
- 就業歧視
- 身障就業
- 勞動行政_其他

- 社會救助（中、低收入戶、急難救助及馬上關懷等）
- 身心障礙福利及復康巴士
- 銀髮族福利、長期照顧及日間照顧
- 兒少福利、弱勢兒少生活扶助、緊急生活扶助及醫療補助
- 住宅租金補貼
- 家庭服務中心
- 家庭暴力、性侵害、兒少保護及性騷擾等防治工作
- 人民團體組織輔導
- 社會救助及社會福利_其他

- 婦女、新住民福利、特殊境遇及補助
- 婦女館、親子館管理及托育服務
- 婦女培力、性別平等業務
- 0–2 歲育兒經濟補助
- 兒童早期療育服務及補助
- 婦幼、新住民福利及健康照顧_其他

- 文化資產問題
- 藝文展演活動
- 圖書館、閱覽室及館舍管理
- 藝文館舍管理
- 文化藝術及圖書管理_其他

- 水、電、瓦斯等公用事業問題
- 工商登記問題
- 市場攤販管理
- 檢舉工廠違規營業
- 檢舉商店違規營業
- 檢舉旅館、民宿違規營業
- 稅務問題
- 工商、經濟及稅務_其他

- 戶政服務
- 殯葬禮儀
- 宗教事務
- 兵役問題
- 民政業務_其他

- 感謝函
- 服務態度問題
- 行政效率問題
- 專業知識問題
- 市府網站或 APP 管理問題
- 感謝函、服務品質及網站／APP 管理問題_其他

- 一般卡申辦問題
- 敬老卡申辦問題
- 愛心卡、愛心陪伴卡申辦問題
- 志工卡申辦問題
- 學生卡申辦問題
- 行動卡、聯名卡申辦問題
- 卡片感應及使用問題
- 市民卡優惠及加值服務建議
- 市民卡業務_其他

- 食品安全衛生
- 醫療管理
- 藥品及化妝品管理
- 傳染病防治及預防接種
- 菸害防制
- 自殺防治及心理健康
- 衛生行政_其他

- 土地徵收
- 土地重劃
- 土地測量
- 地籍圖重測
- 不動產交易
- 土地及建物登記
- 檢舉土地違規使用
- 地政服務_其他

- 消防設備、安全檢查
- 瓦斯桶儲放問題
- 消防栓（設置、移位、告示牌）
- 防火巷違建、堆放雜物
- 消防行政_其他

- 行、收賄
- 行政違失
- 其他瀆職情形

- 其他檢舉案件
- 其他建議、諮詢或陳情
</category_list>
"""

PROMPT_TEMPLATE = """
<prompt>
    <role>
    你是一個隸屬於市政府資訊處的「市政案件 AI 分類引擎核心專家」。你的唯一職責是接收市民陳情案件的文字內容，並以極高的精準度與穩定性，將其分派到正確的業務單位。你的輸出將直接進入自動化派案系統，任何錯誤都可能導致市府資源浪費與民怨。因此，你必須絕對遵循以下的指示與分類原則。
    </role>

    <category_list>
		- 緊急案件_造成民眾生命危險
		- 緊急案件_影響民眾身體健康
		- 緊急案件_暴力威脅事件
		- 緊急案件_涉及婦幼及學童議題
		- 緊急案件_民生需求中斷
		- 緊急案件_其他應緊急處理案件
		- 警政及交通裁罰業務_檢舉交通違規
		- 警政及交通裁罰業務_交通罰單申訴
		- 警政及交通裁罰業務_車輛拖吊爭議
		- 警政及交通裁罰業務_交通疏導或壅塞通報
		- 警政及交通裁罰業務_闖紅燈（超速）照相桿增設或維護
		- 警政及交通裁罰業務_監視器問題
		- 警政及交通裁罰業務_治安維護
		- 警政及交通裁罰業務_妨害風化（俗）
		- 警政及交通裁罰業務_警政風紀
		- 警政及交通裁罰業務_其他
		- 路霸排除_占用道路、騎樓及人行道
		- 路霸排除_有牌廢棄車查報
		- 路霸排除_無牌廢棄車查報
		- 路霸排除_廣告車輛長期占用停車格
		- 路霸排除_違規停車
		- 路霸排除_其他
		- 道路、水溝維護_路面不平整或掏空破洞
		- 道路、水溝維護_路面油漬清除
		- 道路、水溝維護_道路側溝清淤或惡臭處理
		- 道路、水溝維護_水溝溝蓋維修
		- 道路、水溝維護_道路施工時間、交通管制及安全管理問題
		- 道路、水溝維護_道路淹（積）水通報
		- 道路、水溝維護_電纜下地或纜線垂落
		- 道路、水溝維護_孔蓋異音
		- 道路、水溝維護_其他
		- 路燈、路樹及公園管理維護_路燈故障
		- 路燈、路樹及公園管理維護_路燈新增或遷移申請
		- 路燈、路樹及公園管理維護_公園設施損壞
		- 路燈、路樹及公園管理維護_路樹傾倒
		- 路燈、路樹及公園管理維護_公園、綠地及路樹養護
		- 路燈、路樹及公園管理維護_新闢公園建議案
		- 路燈、路樹及公園管理維護_其他
		- 交通號誌、標誌、標線及大眾運輸_交通號誌(紅綠燈)故障或損壞傾斜
		- 交通號誌、標誌、標線及大眾運輸_交通標誌牌面、反光鏡損壞傾斜
		- 交通號誌、標誌、標線及大眾運輸_交通號誌增設、移除或紅綠燈秒數調整
		- 交通號誌、標誌、標線及大眾運輸_交通標誌、標線、反光鏡設置或移除
		- 交通號誌、標誌、標線及大眾運輸_路邊停車格問題
		- 交通號誌、標誌、標線及大眾運輸_停車費問題
		- 交通號誌、標誌、標線及大眾運輸_公車問題
		- 交通號誌、標誌、標線及大眾運輸_站牌、候車亭設施管理
		- 交通號誌、標誌、標線及大眾運輸_免費市民公車（樂活巴）問題
		- 交通號誌、標誌、標線及大眾運輸_計程車問題及招呼站設施管理
		- 交通號誌、標誌、標線及大眾運輸_公車動態系統問題
		- 交通號誌、標誌、標線及大眾運輸_公共自行車（UBike）租賃問題
		- 交通號誌、標誌、標線及大眾運輸_捷運營運及管理
		- 交通號誌、標誌、標線及大眾運輸_捷運建設工程相關問題
		- 交通號誌、標誌、標線及大眾運輸_其他
		- 噪音、污染及環境維護_髒亂點查報
		- 噪音、污染及環境維護_犬貓屍體清除
		- 噪音、污染及環境維護_違規張貼廣告物
		- 噪音、污染及環境維護_廢棄物清運預約
		- 噪音、污染及環境維護_廢棄物管理（含清除、處理、再利用、輸出）
		- 噪音、污染及環境維護_垃圾車清運動線及管理
		- 噪音、污染及環境維護_住宅內人與動物噪音
		- 噪音、污染及環境維護_住家、改裝車噪音
		- 噪音、污染及環境維護_營業場所、工廠及施工噪音
		- 噪音、污染及環境維護_空氣污染
		- 噪音、污染及環境維護_工廠排放廢水、河川污染
		- 噪音、污染及環境維護_綜合性環境污染
		- 噪音、污染及環境維護_其他
		- 建築管理_社會住宅管理
		- 建築管理_一般違建查報
		- 建築管理_興建中違建查報
		- 建築管理_已查報違建拆除問題
		- 建築管理_違規招牌或樹立廣告物查報
		- 建築管理_公寓大廈管理問題
		- 建築管理_建築法規問題
		- 建築管理_建築物公共安全問題
		- 建築管理_領有建造執照施工損鄰
		- 建築管理_其他
		- 動物保護及寵物管理_捕蜂、抓蛇
		- 動物保護及寵物管理_動物受困、受傷通報
		- 動物保護及寵物管理_動物收容及認養問題
		- 動物保護及寵物管理_遛狗未繫繩
		- 動物保護及寵物管理_不當飼養
		- 動物保護及寵物管理_非法業者及業者違規問題
		- 動物保護及寵物管理_其他
		- 教育及體育_教師介聘甄選
		- 教育及體育_高級中等學校問題
		- 教育及體育_國中學校問題
		- 教育及體育_國小學校問題
		- 教育及體育_補教問題
		- 教育及體育_幼兒園問題
		- 教育及體育_社區大學、樂齡學習等終身教育問題
		- 教育及體育_特殊教育問題
		- 教育及體育_學校體育問題
		- 教育及體育_體育活動及場務管理
		- 教育及體育_其他
		- 勞動行政_勞工法令諮詢
		- 勞動行政_勞資糾紛協調
		- 勞動行政_就業服務及職業訓練
		- 勞動行政_移工業務
		- 勞動行政_檢舉公司（雇主）違反勞動法規
		- 勞動行政_就業歧視
		- 勞動行政_身障就業
		- 勞動行政_其他
		- 社會救助及社會福利_社會救助（中、低收入戶、急難救助及馬上關懷等）
		- 社會救助及社會福利_身心障礙福利及復康巴士
		- 社會救助及社會福利_銀髮族福利、長期照顧及日間照顧
		- 社會救助及社會福利_兒少福利、弱勢兒少生活扶助、緊急生活扶助及醫療補助
		- 社會救助及社會福利_住宅租金補貼
		- 社會救助及社會福利_家庭服務中心
		- 社會救助及社會福利_家庭暴力、性侵害、兒少保護及性騷擾等防治工作
		- 社會救助及社會福利_人民團體組織輔導
		- 社會救助及社會福利_其他
		- 婦幼、新住民福利及健康照顧_婦女、新住民福利、特殊境遇及補助
		- 婦幼、新住民福利及健康照顧_婦女館、親子館管理及托育服務
		- 婦幼、新住民福利及健康照顧_婦女培力、性別平等業務
		- 婦幼、新住民福利及健康照顧_0–2 歲育兒經濟補助
		- 婦幼、新住民福利及健康照顧_兒童早期療育服務及補助
		- 婦幼、新住民福利及健康照顧_其他
		- 文化藝術及圖書管理_文化資產問題
		- 文化藝術及圖書管理_藝文展演活動
		- 文化藝術及圖書管理_圖書館、閱覽室及館舍管理
		- 文化藝術及圖書管理_藝文館舍管理
		- 文化藝術及圖書管理_其他
		- 工商、經濟及稅務_水、電、瓦斯等公用事業問題
		- 工商、經濟及稅務_工商登記問題
		- 工商、經濟及稅務_市場攤販管理
		- 工商、經濟及稅務_檢舉工廠違規營業
		- 工商、經濟及稅務_檢舉商店違規營業
		- 工商、經濟及稅務_檢舉旅館、民宿違規營業
		- 工商、經濟及稅務_稅務問題
		- 工商、經濟及稅務_其他
		- 民政業務_戶政服務
		- 民政業務_殯葬禮儀
		- 民政業務_宗教事務
		- 民政業務_兵役問題
		- 民政業務_其他
		- 感謝函、服務品質及網站／APP 管理問題_感謝函
		- 感謝函、服務品質及網站／APP 管理問題_服務態度問題
		- 感謝函、服務品質及網站／APP 管理問題_行政效率問題
		- 感謝函、服務品質及網站／APP 管理問題_專業知識問題
		- 感謝函、服務品質及網站／APP 管理問題_市府網站或 APP 管理問題
		- 感謝函、服務品質及網站／APP 管理問題_其他
		- 市民卡業務_一般卡申辦問題
		- 市民卡業務_敬老卡申辦問題
		- 市民卡業務_愛心卡、愛心陪伴卡申辦問題
		- 市民卡業務_志工卡申辦問題
		- 市民卡業務_學生卡申辦問題
		- 市民卡業務_行動卡、聯名卡申辦問題
		- 市民卡業務_卡片感應及使用問題
		- 市民卡業務_市民卡優惠及加值服務建議
		- 市民卡業務_其他
		- 衛生行政_食品安全衛生
		- 衛生行政_醫療管理
		- 衛生行政_藥品及化妝品管理
		- 衛生行政_傳染病防治及預防接種
		- 衛生行政_菸害防制
		- 衛生行政_自殺防治及心理健康
		- 衛生行政_其他
		- 地政服務_土地徵收
		- 地政服務_土地重劃
		- 地政服務_土地測量
		- 地政服務_地籍圖重測
		- 地政服務_不動產交易
		- 地政服務_土地及建物登記
		- 地政服務_檢舉土地違規使用
		- 地政服務_其他
		- 消防行政_消防設備、安全檢查
		- 消防行政_瓦斯桶儲放問題
		- 消防行政_消防栓（設置、移位、告示牌）
		- 消防行政_防火巷違建、堆放雜物
		- 消防行政_其他
		- 政風行政_行、收賄
		- 政風行政_行政違失
		- 政風行政_其他瀆職情形
		- 其他類別_其他檢舉案件
		- 其他類別_其他建議、諮詢或陳情
    </category_list>

    <instructions>
        <cognitive_framework>
        你必須嚴格遵循以下內部認知框架來處理每一個案件，此過程嚴禁輸出：

        1.  **核心意圖分析 (Intent Analysis)**:
            -   首先，通讀 `<case_content>`，辨識市民的核心訴求。
            -   判斷其主要意圖是「檢舉告發」、「請求協助」、「諮詢問題」、還是「提出建議」。這有助於初步鎖定「主案類」。

        2.  **階層式分類匹配 (Hierarchical Matching)**:
            -   **第一層 (主案類)**: 根據核心意圖與內容關鍵詞，從 `<category_list>` 中選擇**唯一一個**最相關的「主案類」。例如，內容涉及「馬路坑洞」，主案類應為「道路、水溝維護」。
            -   **第二層 (子案類)**: 在已鎖定的主案類下，檢視其所有的「子案類」，選擇一個**最精確描述**案件核心問題的選項。例如，在「道路、水溝維護」下，「路面不平整或掏空破洞」會是最佳選擇。

        3.  **決策捷徑與例外處理 (Heuristics & Exception Handling)**:
            -   **`..._其他` 的使用**: 如果在選定的「主案類」中，找不到任何一個精確匹配的「子案類」，必須優先選擇該主案類的 `..._其他` 選項。**嚴禁**為了匹配而選擇一個不完全貼切的子案類。
            -   **最終備用選項**: 僅在**沒有任何一個「主案類」**能合理涵蓋案件內容時，才可使用最終的「其他類別_其他建議、諮詢或陳情」。

        4.  **最終格式化 (Final Formatting)**:
            -   將最終選定的「主案類_子案類」完整字串，放入指定的 JSON 結構中。
            -   再次確認類別名稱與 `<category_list>` 中的條目一字不差。
        </cognitive_framework>
    </instructions>

    <examples>
    以下是幾個成功分類的範例，請學習其分類邏輯：

    1.  **案件內容**: "我家門口的路燈整個晚上都沒亮，黑漆漆的，老人家走路很危險，麻煩幫忙修一下。"
        **輸出**: `{ "category": "路燈、路樹及公園管理維護_路燈故障" }`
        **邏輯**: 明確的路燈損壞請求，直接對應。

    2.  **案件內容**: "週末去 A 公園散步，覺得裡面的花草有點稀疏，椅子上也有點髒污，希望市府能多用點心維護市容。"
        **輸出**: `{ "category": "路燈、路樹及公園管理維護_其他" }`
        **邏輯**: 屬於公園養護的建議，但未涉及具體的「設施損壞」或「路樹傾倒」，因此在該主案類下選擇「其他」最為恰當。

    3.  **案件內容**: "隔壁的餐廳把桌椅都擺到人行道上做生意，行人都要走到馬路上，真的很誇張！"
        **輸出**: `{ "category": "路霸排除_占用道路、騎樓及人行道" }`
        **邏輯**: 核心問題是「占用人行道」，完全符合路霸排除的定義。

    4.  **案件內容**: "我對現在的勞工政策有些想法，希望能建議市長多關注青年低薪問題，我覺得可以從...開始著手。"
        **輸出**: `{ "category": "其他類別_其他建議、諮詢或陳情" }`
        **邏輯**: 案件內容是廣泛的政策建議，不屬於任何一個特定的業務主案類（如勞資糾紛或檢舉），因此使用最終的備用選項。
    </examples>

    <output_format>
    -   你的回應**必須且只能是**一個 JSON 物件。
    -   JSON 物件**絕不能**包含任何 Markdown 標記 (例如 ```json ... ```)。
    -   JSON 物件的結構必須嚴格遵守：`{ "category": "<選擇的類別名稱>" }`。
    -   不要輸出任何其他文字、解釋或註解。
    </output_format>

    <case_to_classify>
        <case_content>
        {case_content}
        </case_content>
    </case_to_classify>
</prompt>
"""


@retry(
    stop=stop_after_attempt(3),
    wait=wait_fixed(2),
    retry=retry_if_exception_type(openai.APIError),
    before_sleep=before_sleep_log(logger, logging.WARNING),
)
def get_ai_category_response(
    petition_content: str,
) -> tuple[AICategoryResponse | None, TokenUsage]:
    """
    Calls the OpenAI API to get a category for the given petition content.
    Includes retry logic for API errors.

    Args:
        petition_content: The content of the petition.

    Returns:
        A tuple of (AICategoryResponse object, TokenUsage) or (None, TokenUsage) if all retries fail.
    """
    token_usage = TokenUsage()

    try:
        client = instructor.from_provider(
            "openai/gpt-4o-mini",
        )
        prompt = format_prompt(petition_content)

        response = _create_chat_completion_with_retry(client, prompt)

        if response:
            # Try different ways to access usage information
            usage_info = None
            if hasattr(response, "usage"):
                usage_info = response.usage
            elif hasattr(response, "_raw_response") and hasattr(
                response._raw_response, "usage"
            ):
                # 使用Instruct套件的話, 要去_raw_response取得usage欄位
                usage_info = response._raw_response.usage
            elif hasattr(response, "raw") and hasattr(response.raw, "usage"):
                usage_info = response.raw.usage

            if usage_info:
                token_usage.prompt_tokens = usage_info.prompt_tokens
                token_usage.completion_tokens = usage_info.completion_tokens
                token_usage.total_tokens = usage_info.total_tokens
                token_usage.total_cost_usd = calculate_cost(
                    token_usage.prompt_tokens, token_usage.completion_tokens
                )
            else:
                logger.error("No usage information found in response")
                # Debug: print response attributes
                logger.debug(f"Response type: {type(response)}")
                logger.debug(f"Response attributes: {dir(response)}")

        return response, token_usage
    except RetryError:
        logger.error("All retry attempts failed for getting AI category response.")
        return None, token_usage


@retry(
    stop=stop_after_attempt(3),
    wait=wait_fixed(2),
    retry=retry_if_exception_type(openai.APIError),
    before_sleep=before_sleep_log(logger, logging.WARNING),
)
def _create_chat_completion_with_retry(client, prompt):
    return client.chat.completions.create(
        response_model=AICategoryResponse,
        messages=[
            {"role": "user", "content": prompt},
        ],
    )


def format_prompt(petition_content: str) -> str:
    """
    Formats the prompt template by injecting the petition content.

    Args:
        petition_content: The content of the petition.

    Returns:
        The formatted prompt string.
    """
    return PROMPT_TEMPLATE.replace("{case_content}", petition_content)
