import argparse
import logging

from dotenv import load_dotenv

from use_case.categorize_petitions_use_case import categorize_petitions_use_case

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """
    Main function to run the petition categorization script.
    """
    parser = argparse.ArgumentParser(
        description="Categorize petitions from a JSON file."
    )
    parser.add_argument(
        "--input",
        type=str,
        # required=True,
        default=r"D:\Users\leamo\work\projects\1999\data-etl\ner-worker\src\data\export_2025-06-01_to_2025-06-01.json",
        help="Path to the input JSON file containing petition data.",
    )
    parser.add_argument(
        "--output",
        type=str,
        # required=True,
        default=r"D:\Users\leamo\work\projects\1999\data-etl\ner-worker\src\data\2025-06-01_to_2025-06-01_ai.json",
        help="Path to the output JSON file to save the results.",
    )
    args = parser.parse_args()

    logger.info("Starting petition categorization process...")
    logger.info(f"Input file: {args.input}")
    logger.info(f"Output file: {args.output}")

    try:
        categorize_petitions_use_case(args.input, args.output)
        logger.info(f"Successfully processed petitions. Output saved to {args.output}")
    except FileNotFoundError:
        logger.error(f"Error: Input file not found at {args.input}")
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")


if __name__ == "__main__":
    load_dotenv()
    main()
